import React, { useState } from 'react';
import { FaFacebook, FaTwitter, FaPhone, FaEnvelope, FaMapMarkerAlt, FaExternalLinkAlt, FaTimes, FaWhatsapp, FaBicycle, FaCalendarAlt, FaMapMarkerAlt as FaLocation, FaHeart, FaCopy } from 'react-icons/fa';
import './index.css';

function App() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState('');

  // Order form state
  const [orderFormData, setOrderFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    product: '',
    quantity: 1,
    notes: ''
  });
  const [isOrderModalOpen, setIsOrderModalOpen] = useState(false);
  const [isOrderSubmitting, setIsOrderSubmitting] = useState(false);
  const [orderSubmitStatus, setOrderSubmitStatus] = useState('');

  // Cycling event banner state
  const [isCyclingModalOpen, setIsCyclingModalOpen] = useState(false);

  // Donation modal state
  const [isDonationModalOpen, setIsDonationModalOpen] = useState(false);
  const [copySuccess, setCopySuccess] = useState('');
  const [isDonationModalOpen, setIsDonationModalOpen] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleOrderInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setOrderFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus('');

    try {
      // Create mailto link with form data
      const subject = encodeURIComponent('Contact from Samaki Cookies Website');
      const body = encodeURIComponent(
        `Name: ${formData.name}\nEmail: ${formData.email}\n\nMessage:\n${formData.message}`
      );
      const mailtoLink = `mailto:<EMAIL>?subject=${subject}&body=${body}`;

      // Open email client
      window.location.href = mailtoLink;

      // Reset form
      setFormData({ name: '', email: '', message: '' });
      setSubmitStatus('success');
    } catch (error) {
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOrderSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsOrderSubmitting(true);
    setOrderSubmitStatus('');

    try {
      // Create mailto link with order data
      const subject = encodeURIComponent(`New Order: ${orderFormData.product} - Samaki Cookies`);
      const body = encodeURIComponent(
        `NEW ORDER DETAILS:\n\n` +
        `Customer Name: ${orderFormData.name}\n` +
        `Email: ${orderFormData.email}\n` +
        `Phone: ${orderFormData.phone}\n` +
        `Delivery Address: ${orderFormData.address}\n\n` +
        `ORDER INFORMATION:\n` +
        `Product: ${orderFormData.product}\n` +
        `Quantity: ${orderFormData.quantity}\n` +
        `Special Notes: ${orderFormData.notes || 'None'}\n\n` +
        `Please contact the customer to confirm the order and arrange delivery.`
      );
      const mailtoLink = `mailto:<EMAIL>?subject=${subject}&body=${body}`;

      // Open email client
      window.location.href = mailtoLink;

      // Reset form and close modal
      setOrderFormData({
        name: '',
        email: '',
        phone: '',
        address: '',
        product: '',
        quantity: 1,
        notes: ''
      });
      setOrderSubmitStatus('success');
      setTimeout(() => {
        setIsOrderModalOpen(false);
        setOrderSubmitStatus('');
      }, 2000);
    } catch (error) {
      setOrderSubmitStatus('error');
    } finally {
      setIsOrderSubmitting(false);
    }
  };

  const openOrderModal = (productName: string, price: string) => {
    setOrderFormData(prev => ({
      ...prev,
      product: `${productName} (${price})`
    }));
    setIsOrderModalOpen(true);
  };

  // WhatsApp functions
  const openWhatsAppContact = () => {
    const message = encodeURIComponent(
      "Hello! I'm interested in learning more about Samaki Cookies. Could you please provide more information about your products?"
    );
    const whatsappUrl = `https://wa.me/254704668776?text=${message}`;
    window.open(whatsappUrl, '_blank');
  };

  const openWhatsAppOrder = (productName: string, price: string) => {
    const message = encodeURIComponent(
      `Hello! I would like to place an order for ${productName} (${price}). Could you please help me with the ordering process?`
    );
    const whatsappUrl = `https://wa.me/254704668776?text=${message}`;
    window.open(whatsappUrl, '_blank');
  };

  // Copy to clipboard function
  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopySuccess(`${type} copied!`);
      setTimeout(() => setCopySuccess(''), 2000);
    }).catch(() => {
      setCopySuccess('Failed to copy');
      setTimeout(() => setCopySuccess(''), 2000);
    });
  };

  return (
    <div className="font-sans">
      {/* Navigation */}
      <nav className="bg-white shadow-md fixed w-full z-10">
        <div className="container mx-auto px-4 py-3 flex justify-between items-center">
          <div className="text-2xl font-bold text-amber-800">Samaki Cookies</div>
          <div className="hidden md:flex space-x-8 items-center">
            <a href="#home" className="text-amber-900 hover:text-amber-600 transition">Home</a>
            <a href="#about" className="text-amber-900 hover:text-amber-600 transition">About</a>
            <a href="#products" className="text-amber-900 hover:text-amber-600 transition">Products</a>
            <a href="#gallery" className="text-amber-900 hover:text-amber-600 transition">Gallery</a>
            <a href="#story" className="text-amber-900 hover:text-amber-600 transition">Our Story</a>
            <a href="#contact" className="text-amber-900 hover:text-amber-600 transition">Contact</a>
            <button
              onClick={() => setIsDonationModalOpen(true)}
              className="bg-red-600 text-white px-4 py-2 rounded-full hover:bg-red-700 transition flex items-center space-x-2 animate-pulse"
            >
              <FaHeart className="text-sm" />
              <span className="font-semibold">Donate</span>
            </button>
          </div>
          <div className="md:hidden flex items-center space-x-3">
            <button
              onClick={() => setIsDonationModalOpen(true)}
              className="bg-red-600 text-white px-3 py-2 rounded-full hover:bg-red-700 transition flex items-center space-x-1 animate-pulse"
            >
              <FaHeart className="text-xs" />
              <span className="text-sm font-semibold">Donate</span>
            </button>
            <button className="text-amber-900">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16"></path>
              </svg>
            </button>
          </div>
        </div>
      </nav>

      {/* Cycling Event Banner */}
      <div className="fixed top-20 left-4 right-4 z-40 md:left-8 md:right-8">
        <div
          className="bg-gradient-to-r from-green-600 to-blue-600 rounded-lg shadow-2xl cursor-pointer transform hover:scale-105 transition-all duration-300 overflow-hidden"
          onClick={() => setIsCyclingModalOpen(true)}
        >
          <div className="flex flex-col md:flex-row items-center p-4">
            <div className="flex-shrink-0 mb-3 md:mb-0 md:mr-4">
              <img
                src="/banner.jpeg"
                alt="Pedal for Change Cycling Event"
                className="w-20 h-20 md:w-24 md:h-24 rounded-full object-cover border-4 border-white shadow-lg"
                onError={(e) => {
                  // Fallback to a cycling icon if image not found
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.nextElementSibling.style.display = 'flex';
                }}
              />
              <div className="w-20 h-20 md:w-24 md:h-24 rounded-full bg-white flex items-center justify-center border-4 border-white shadow-lg hidden">
                <FaBicycle className="text-green-600 text-3xl" />
              </div>
            </div>
            <div className="flex-1 text-center md:text-left">
              <h3 className="text-white font-bold text-lg md:text-xl mb-1">
                🚴‍♂️ Pedal for Change: Cycling for Agroecology, Peace & Nutrition
              </h3>
              <p className="text-green-100 text-sm md:text-base mb-2">
                Fueling Young Minds with Samaki Cookies - November 27th, 2025
              </p>
              <div className="flex flex-wrap justify-center md:justify-start gap-2 text-xs md:text-sm text-white">
                <span className="bg-white bg-opacity-20 px-2 py-1 rounded-full flex items-center">
                  <FaCalendarAlt className="mr-1" /> Nov 27, 2025
                </span>
                <span className="bg-white bg-opacity-20 px-2 py-1 rounded-full flex items-center">
                  <FaLocation className="mr-1" /> Mkoroshoni to Karisa Maitha
                </span>
                <span className="bg-white bg-opacity-20 px-2 py-1 rounded-full flex items-center">
                  <FaBicycle className="mr-1" /> 10km Ride
                </span>
              </div>
            </div>
            <div className="flex-shrink-0 mt-3 md:mt-0 md:ml-4">
              <div className="bg-white text-green-600 px-4 py-2 rounded-full font-bold text-sm hover:bg-green-50 transition-colors">
                Learn More →
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Cycling Event Modal */}
      {isCyclingModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="relative">
              {/* Header with banner image */}
              <div className="relative h-64 bg-gradient-to-r from-green-600 to-blue-600 rounded-t-lg overflow-hidden">
                <img
                  src="/banner.jpeg"
                  alt="Pedal for Change Banner"
                  className="w-full h-full object-cover opacity-80"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                  }}
                />
                <div className="absolute inset-0 bg-gradient-to-r from-green-600 to-blue-600 opacity-80"></div>
                <div className="absolute inset-0 flex items-center justify-center text-center text-white p-6">
                  <div>
                    <FaBicycle className="text-6xl mb-4 mx-auto" />
                    <h2 className="text-3xl md:text-4xl font-bold mb-2">Pedal for Change</h2>
                    <p className="text-xl">Cycling for Agroecology, Peace & Nutrition</p>
                  </div>
                </div>
                <button
                  onClick={() => setIsCyclingModalOpen(false)}
                  className="absolute top-4 right-4 text-white hover:text-gray-300 transition"
                >
                  <FaTimes size={24} />
                </button>
              </div>

              {/* Content */}
              <div className="p-6">
                <div className="text-center mb-6">
                  <h3 className="text-2xl font-bold text-green-800 mb-2">Fueling Young Minds with Samaki Cookies</h3>
                  <div className="w-24 h-1 bg-green-500 mx-auto"></div>
                </div>

                <div className="grid md:grid-cols-2 gap-6 mb-6">
                  <div className="bg-green-50 p-6 rounded-lg">
                    <h4 className="text-xl font-semibold text-green-800 mb-4 flex items-center">
                      <FaCalendarAlt className="mr-2" /> Event Details
                    </h4>
                    <div className="space-y-3 text-gray-700">
                      <p><strong>Date:</strong> November 27th, 2025</p>
                      <p><strong>Route:</strong> Mkoroshoni Market to Karisa Maitha Ground</p>
                      <p><strong>Distance:</strong> 10km</p>
                      <p><strong>Entry:</strong> Optional donation/ticketing</p>
                      <p><strong>Organizer:</strong> SEABERRY SNACKS LIMITED</p>
                    </div>
                  </div>

                  <div className="bg-blue-50 p-6 rounded-lg">
                    <h4 className="text-xl font-semibold text-blue-800 mb-4">Our Mission</h4>
                    <p className="text-gray-700 mb-4">
                      Support <strong>100 malnourished children</strong> to return to school with nutritional support using Samaki Cookies.
                    </p>
                    <div className="space-y-2 text-sm text-gray-600">
                      <p>✓ Promote agroecological farming practices</p>
                      <p>✓ Foster peace, unity, and community participation</p>
                      <p>✓ Advocate for locally made nutritional products</p>
                    </div>
                  </div>
                </div>

                <div className="bg-amber-50 p-6 rounded-lg mb-6">
                  <h4 className="text-xl font-semibold text-amber-800 mb-4">Event Activities</h4>
                  <div className="grid md:grid-cols-2 gap-4 text-gray-700">
                    <div>
                      <p>🚴‍♂️ Cycling rally</p>
                      <p>🌱 Agroecology demo booths</p>
                      <p>🍪 Samaki Cookies tasting & nutrition education</p>
                    </div>
                    <div>
                      <p>🌳 Peace tree planting</p>
                      <p>🎤 Speeches by nutrition champions</p>
                      <p>🏆 Certificates and awards</p>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 p-6 rounded-lg mb-6">
                  <h4 className="text-xl font-semibold text-gray-800 mb-4">Budget & Impact</h4>
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <h5 className="font-semibold text-gray-700 mb-2">Total Budget: KES 400,000</h5>
                      <ul className="text-sm text-gray-600 space-y-1">
                        <li>• Event logistics: KES 100,000</li>
                        <li>• Publicity & materials: KES 100,000</li>
                        <li>• Nutritional support (3 months): KES 100,000</li>
                        <li>• Certificates & volunteers: KES 20,000</li>
                        <li>• Public Address system: KES 35,000</li>
                      </ul>
                    </div>
                    <div>
                      <h5 className="font-semibold text-gray-700 mb-2">Expected Impact</h5>
                      <ul className="text-sm text-gray-600 space-y-1">
                        <li>• 100+ children supported with nutrition</li>
                        <li>• Community empowered with agroecology knowledge</li>
                        <li>• Improved peace building awareness</li>
                        <li>• Strengthened local nutrition initiatives</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="text-center">
                  <div className="mb-4">
                    <h4 className="text-lg font-semibold text-gray-800 mb-2">Get Involved</h4>
                    <p className="text-gray-600 mb-4">Join us in making a difference in children's lives through cycling and nutrition!</p>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <button
                      onClick={() => {
                        const message = encodeURIComponent(
                          "Hello! I'm interested in participating in the 'Pedal for Change' cycling event on November 27th, 2025. Could you please provide more information about registration and how I can get involved?"
                        );
                        const whatsappUrl = `https://wa.me/254704668776?text=${message}`;
                        window.open(whatsappUrl, '_blank');
                      }}
                      className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition flex items-center justify-center"
                    >
                      <FaWhatsapp className="mr-2" /> Join via WhatsApp
                    </button>

                    <button
                      onClick={() => {
                        const subject = encodeURIComponent('Pedal for Change - Cycling Event Inquiry');
                        const body = encodeURIComponent(
                          `Hello Francis,\n\nI'm interested in the "Pedal for Change: Cycling for Agroecology, Peace & Nutrition" event scheduled for November 27th, 2025.\n\nPlease provide more information about:\n- Registration process\n- Participation requirements\n- How I can contribute to supporting the 100 malnourished children\n- Sponsorship opportunities\n\nThank you for organizing this meaningful initiative!\n\nBest regards`
                        );
                        const mailtoLink = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
                        window.location.href = mailtoLink;
                      }}
                      className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition flex items-center justify-center"
                    >
                      <FaEnvelope className="mr-2" /> Email Inquiry
                    </button>
                  </div>

                  <div className="mt-4 text-sm text-gray-600">
                    <p><strong>Contact:</strong> Francis Thoya - Sea berry Snacks</p>
                    <p>Phone: <a href="tel:0704668776" className="text-green-600 hover:underline">0704668776</a></p>
                    <p>Email: <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Donation Modal */}
      {isDonationModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="relative">
              {/* Header */}
              <div className="bg-gradient-to-r from-red-600 to-pink-600 rounded-t-lg p-6 text-center text-white">
                <FaHeart className="text-4xl mb-3 mx-auto" />
                <h2 className="text-2xl font-bold mb-2">Support Our Mission</h2>
                <p className="text-red-100">Help us feed malnourished children with Samaki Cookies</p>
                <button
                  onClick={() => setIsDonationModalOpen(false)}
                  className="absolute top-4 right-4 text-white hover:text-red-200 transition"
                >
                  <FaTimes size={20} />
                </button>
              </div>

              {/* Content */}
              <div className="p-6">
                <div className="text-center mb-6">
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">Make a Difference Today</h3>
                  <p className="text-gray-600 text-sm">
                    Your donation helps provide nutritious Samaki Cookies to malnourished children,
                    supporting their health, education, and future.
                  </p>
                </div>

                <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                  <h4 className="font-semibold text-green-800 mb-3 flex items-center">
                    <span className="bg-green-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-2">M</span>
                    M-Pesa Donation Details
                  </h4>

                  <div className="space-y-3">
                    <div className="flex justify-between items-center bg-white p-3 rounded border">
                      <div>
                        <span className="text-sm text-gray-600">Paybill Number:</span>
                        <div className="font-bold text-lg text-green-700">625625</div>
                      </div>
                      <button
                        onClick={() => copyToClipboard('625625', 'Paybill number')}
                        className="bg-green-100 hover:bg-green-200 text-green-700 p-2 rounded transition"
                        title="Copy Paybill Number"
                      >
                        <FaCopy />
                      </button>
                    </div>

                    <div className="flex justify-between items-center bg-white p-3 rounded border">
                      <div>
                        <span className="text-sm text-gray-600">Account Number:</span>
                        <div className="font-bold text-lg text-green-700">********</div>
                      </div>
                      <button
                        onClick={() => copyToClipboard('********', 'Account number')}
                        className="bg-green-100 hover:bg-green-200 text-green-700 p-2 rounded transition"
                        title="Copy Account Number"
                      >
                        <FaCopy />
                      </button>
                    </div>
                  </div>

                  {copySuccess && (
                    <div className="mt-3 p-2 bg-green-100 border border-green-300 text-green-700 rounded text-sm text-center">
                      ✓ {copySuccess}
                    </div>
                  )}
                </div>

                <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6">
                  <h4 className="font-semibold text-amber-800 mb-2">How to Donate via M-Pesa:</h4>
                  <ol className="text-sm text-gray-700 space-y-1">
                    <li>1. Go to M-Pesa menu on your phone</li>
                    <li>2. Select "Lipa na M-Pesa"</li>
                    <li>3. Select "Pay Bill"</li>
                    <li>4. Enter Paybill: <strong>625625</strong></li>
                    <li>5. Enter Account: <strong>********</strong></li>
                    <li>6. Enter your donation amount</li>
                    <li>7. Enter your M-Pesa PIN</li>
                  </ol>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                  <h4 className="font-semibold text-blue-800 mb-2">Your Impact:</h4>
                  <div className="text-sm text-gray-700 space-y-1">
                    <p>• <strong>KES 50</strong> - Feeds 1 child for a week</p>
                    <p>• <strong>KES 200</strong> - Feeds 1 child for a month</p>
                    <p>• <strong>KES 500</strong> - Supports 1 child's nutrition for 3 months</p>
                    <p>• <strong>KES 1,000</strong> - Helps 5 children return to school with proper nutrition</p>
                  </div>
                </div>

                <div className="text-center">
                  <p className="text-sm text-gray-600 mb-4">
                    For questions about donations or to get involved, contact us:
                  </p>

                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <button
                      onClick={() => {
                        const message = encodeURIComponent(
                          "Hello! I would like to make a donation to support Samaki Cookies and help malnourished children. Could you please provide more information about how my donation will be used?"
                        );
                        const whatsappUrl = `https://wa.me/254704668776?text=${message}`;
                        window.open(whatsappUrl, '_blank');
                      }}
                      className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition flex items-center justify-center"
                    >
                      <FaWhatsapp className="mr-2" /> WhatsApp Us
                    </button>

                    <button
                      onClick={() => {
                        const subject = encodeURIComponent('Donation Inquiry - Samaki Cookies');
                        const body = encodeURIComponent(
                          `Hello Francis,\n\nI'm interested in making a donation to support Samaki Cookies and help provide nutrition to malnourished children.\n\nCould you please provide more information about:\n- How donations are used\n- Impact reports\n- Other ways I can help\n\nThank you for your important work!\n\nBest regards`
                        );
                        const mailtoLink = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
                        window.location.href = mailtoLink;
                      }}
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition flex items-center justify-center"
                    >
                      <FaEnvelope className="mr-2" /> Email Us
                    </button>
                  </div>

                  <div className="mt-4 text-xs text-gray-500">
                    <p>Samaki Cookies - Combating malnutrition one cookie at a time</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Order Modal */}
      {isOrderModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-2xl font-bold text-amber-900">Place Your Order</h3>
                <button
                  onClick={() => setIsOrderModalOpen(false)}
                  className="text-gray-500 hover:text-gray-700 transition"
                >
                  <FaTimes size={24} />
                </button>
              </div>

              <form onSubmit={handleOrderSubmit} className="space-y-4">
                <div>
                  <label htmlFor="orderName" className="block text-amber-900 font-medium mb-2">Full Name *</label>
                  <input
                    type="text"
                    id="orderName"
                    name="name"
                    value={orderFormData.name}
                    onChange={handleOrderInputChange}
                    required
                    className="w-full px-4 py-2 border border-amber-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
                  />
                </div>

                <div>
                  <label htmlFor="orderEmail" className="block text-amber-900 font-medium mb-2">Email Address *</label>
                  <input
                    type="email"
                    id="orderEmail"
                    name="email"
                    value={orderFormData.email}
                    onChange={handleOrderInputChange}
                    required
                    className="w-full px-4 py-2 border border-amber-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
                  />
                </div>

                <div>
                  <label htmlFor="orderPhone" className="block text-amber-900 font-medium mb-2">Phone Number *</label>
                  <input
                    type="tel"
                    id="orderPhone"
                    name="phone"
                    value={orderFormData.phone}
                    onChange={handleOrderInputChange}
                    required
                    placeholder="+254..."
                    className="w-full px-4 py-2 border border-amber-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
                  />
                </div>

                <div>
                  <label htmlFor="orderAddress" className="block text-amber-900 font-medium mb-2">Delivery Address *</label>
                  <textarea
                    id="orderAddress"
                    name="address"
                    value={orderFormData.address}
                    onChange={handleOrderInputChange}
                    required
                    rows={3}
                    placeholder="Please provide your full delivery address including city and postal code"
                    className="w-full px-4 py-2 border border-amber-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
                  ></textarea>
                </div>

                <div>
                  <label htmlFor="orderProduct" className="block text-amber-900 font-medium mb-2">Product</label>
                  <input
                    type="text"
                    id="orderProduct"
                    name="product"
                    value={orderFormData.product}
                    readOnly
                    className="w-full px-4 py-2 border border-amber-300 rounded-lg bg-gray-50"
                  />
                </div>

                <div>
                  <label htmlFor="orderQuantity" className="block text-amber-900 font-medium mb-2">Quantity</label>
                  <select
                    id="orderQuantity"
                    name="quantity"
                    value={orderFormData.quantity}
                    onChange={handleOrderInputChange}
                    className="w-full px-4 py-2 border border-amber-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
                  >
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(num => (
                      <option key={num} value={num}>{num}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label htmlFor="orderNotes" className="block text-amber-900 font-medium mb-2">Special Instructions (Optional)</label>
                  <textarea
                    id="orderNotes"
                    name="notes"
                    value={orderFormData.notes}
                    onChange={handleOrderInputChange}
                    rows={3}
                    placeholder="Any special requests or delivery instructions..."
                    className="w-full px-4 py-2 border border-amber-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
                  ></textarea>
                </div>

                {orderSubmitStatus === 'success' && (
                  <div className="p-3 bg-green-100 border border-green-400 text-green-700 rounded">
                    Order submitted successfully! We'll contact you soon to confirm your order.
                  </div>
                )}

                {orderSubmitStatus === 'error' && (
                  <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                    There was an error submitting your order. Please try again or call us directly.
                  </div>
                )}

                <div className="flex space-x-4 pt-4">
                  <button
                    type="button"
                    onClick={() => setIsOrderModalOpen(false)}
                    className="flex-1 px-4 py-2 border border-amber-300 text-amber-700 rounded-lg hover:bg-amber-50 transition"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isOrderSubmitting}
                    className="flex-1 bg-amber-700 text-white px-4 py-2 rounded-lg hover:bg-amber-800 transition disabled:opacity-50"
                  >
                    {isOrderSubmitting ? 'Submitting...' : 'Place Order'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Hero Section */}
      <section id="home" className="pt-24 pb-12 bg-amber-100 min-h-screen flex items-center">
        <div className="container mx-auto px-4 flex flex-col md:flex-row items-center">
          <div className="md:w-1/2 mb-8 md:mb-0 md:pr-8">
            <h1 className="text-4xl md:text-5xl font-bold text-amber-900 mb-4">Nutritious Fish Cookies for Healthier Kids</h1>
            <p className="text-lg text-amber-800 mb-6">Samaki Cookies combines the nutritional benefits of fish with the delicious taste of cookies to combat malnutrition in Kenya.</p>
            <div className="flex space-x-4">
              <a href="#products" className="bg-amber-700 text-white px-6 py-3 rounded-lg hover:bg-amber-800 transition">Our Products</a>
              <a href="#story" className="border border-amber-700 text-amber-700 px-6 py-3 rounded-lg hover:bg-amber-700 hover:text-white transition">Our Story</a>
            </div>
          </div>
          <div className="md:w-1/2 flex justify-center">
            <img src="/WhatsApp Image 2025-06-30 at 8.23.56 PM.jpeg" alt="Child enjoying Samaki Cookies" className="rounded-lg shadow-xl max-w-sm w-full h-auto object-cover" />
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-amber-900 mb-4">About Samaki Cookies</h2>
            <div className="w-24 h-1 bg-amber-500 mx-auto"></div>
          </div>

          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-8 md:mb-0">
              <img src="/WhatsApp Image 2025-06-30 at 8.22.11 PM.jpeg" alt="Samaki Cookies Team and Community" className="rounded-lg shadow-xl w-full max-w-md mx-auto" />
            </div>
            <div className="md:w-1/2 md:pl-12">
              <h3 className="text-2xl font-semibold text-amber-800 mb-4">Innovative Nutrition Solution</h3>
              <p className="text-gray-700 mb-4">
                Samaki Cookies is a revolutionary food product that combines the nutritional benefits of fish with the
                appealing taste of cookies. Founded in Kilifi County, Kenya, our mission is to address
                malnutrition while creating delicious, affordable snacks.
              </p>
              <p className="text-gray-700 mb-6">
                Our cookies are made with locally sourced fish and cassava, providing essential proteins, omega-3 fatty acids,
                and other nutrients crucial for child development. We're committed to improving health outcomes while
                supporting local communities.
              </p>
              <div className="flex flex-wrap gap-4">
                <div className="bg-amber-100 px-4 py-3 rounded-lg">
                  <span className="font-bold text-amber-900">High in Protein</span>
                </div>
                <div className="bg-amber-100 px-4 py-3 rounded-lg">
                  <span className="font-bold text-amber-900">Rich in Omega-3</span>
                </div>
                <div className="bg-amber-100 px-4 py-3 rounded-lg">
                  <span className="font-bold text-amber-900">Locally Sourced</span>
                </div>
                <div className="bg-amber-100 px-4 py-3 rounded-lg">
                  <span className="font-bold text-amber-900">Affordable</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Products Section */}
      <section id="products" className="py-16 bg-amber-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-amber-900 mb-4">Our Products</h2>
            <div className="w-24 h-1 bg-amber-500 mx-auto mb-6"></div>
            <p className="text-gray-700 max-w-3xl mx-auto">
              Our fish cookies are carefully crafted to provide essential nutrients while maintaining a delicious taste that children love.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              <img src="/WhatsApp Image 2025-06-30 at 6.16.54 PM.jpeg" alt="Original Fish Cookies" className="w-full h-64 object-cover" />
              <div className="p-6">
                <h3 className="text-xl font-semibold text-amber-900 mb-2">Original Fish Cookies</h3>
                <p className="text-gray-700 mb-4">Our signature cookies made with premium fish and cassava flour, providing essential nutrients in a delicious package.</p>
                <div className="flex justify-between items-center mb-4">
                  <span className="text-amber-800 font-bold">KSh 150</span>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => openOrderModal('Original Fish Cookies', 'KSh 150')}
                    className="flex-1 bg-amber-700 text-white px-4 py-2 rounded hover:bg-amber-800 transition"
                  >
                    Order Now
                  </button>
                  <button
                    onClick={() => openWhatsAppOrder('Original Fish Cookies', 'KSh 150')}
                    className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition flex items-center justify-center"
                    title="Order via WhatsApp"
                  >
                    <FaWhatsapp size={18} />
                  </button>
                </div>
              </div>
            </div>
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              <img src="/WhatsApp Image 2025-06-30 at 6.16.54 PM.jpeg" alt="School Pack" className="w-full h-64 object-cover" />
              <div className="p-6">
                <h3 className="text-xl font-semibold text-amber-900 mb-2">School Nutrition Pack</h3>
                <p className="text-gray-700 mb-4">Specially designed packs for schools and institutions, helping provide balanced nutrition to students.</p>
                <div className="flex justify-between items-center mb-4">
                  <span className="text-amber-800 font-bold">KSh 500</span>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => openOrderModal('School Nutrition Pack', 'KSh 500')}
                    className="flex-1 bg-amber-700 text-white px-4 py-2 rounded hover:bg-amber-800 transition"
                  >
                    Order Now
                  </button>
                  <button
                    onClick={() => openWhatsAppOrder('School Nutrition Pack', 'KSh 500')}
                    className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition flex items-center justify-center"
                    title="Order via WhatsApp"
                  >
                    <FaWhatsapp size={18} />
                  </button>
                </div>
              </div>
            </div>
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              <img src="/WhatsApp Image 2025-06-30 at 6.16.54 PM.jpeg" alt="Family Pack" className="w-full h-64 object-cover" />
              <div className="p-6">
                <h3 className="text-xl font-semibold text-amber-900 mb-2">Family Value Pack</h3>
                <p className="text-gray-700 mb-4">Larger packs perfect for families, ensuring everyone gets the nutritional benefits of our fish cookies.</p>
                <div className="flex justify-between items-center mb-4">
                  <span className="text-amber-800 font-bold">KSh 800</span>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => openOrderModal('Family Value Pack', 'KSh 800')}
                    className="flex-1 bg-amber-700 text-white px-4 py-2 rounded hover:bg-amber-800 transition"
                  >
                    Order Now
                  </button>
                  <button
                    onClick={() => openWhatsAppOrder('Family Value Pack', 'KSh 800')}
                    className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition flex items-center justify-center"
                    title="Order via WhatsApp"
                  >
                    <FaWhatsapp size={18} />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section id="gallery" className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-amber-900 mb-4">Gallery</h2>
            <div className="w-24 h-1 bg-amber-500 mx-auto mb-6"></div>
            <p className="text-gray-700 max-w-3xl mx-auto">
              Take a look at our journey, from research and development to production and community impact. See how Samaki Cookies is making a difference in the lives of children and families across Kenya.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Gallery Item 1 - Lab Research */}
            <div className="group relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300">
              <img
                src="/WhatsApp Image 2025-06-30 at 6.16.47 PM (5).jpeg"
                alt="Research and Development Lab"
                className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity duration-300 flex items-end">
                <div className="p-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <h4 className="font-semibold">Research & Development</h4>
                  <p className="text-sm">Our team conducting nutritional research in the lab</p>
                </div>
              </div>
            </div>

            {/* Gallery Item 2 - Production Process */}
            <div className="group relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300">
              <img
                src="/WhatsApp Image 2025-06-30 at 6.16.51 PM (1).jpeg"
                alt="Cookie Production Process"
                className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity duration-300 flex items-end">
                <div className="p-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <h4 className="font-semibold">Production Process</h4>
                  <p className="text-sm">Francis preparing fresh batch of Samaki Cookies</p>
                </div>
              </div>
            </div>

            {/* Gallery Item 3 - Team Collaboration */}
            <div className="group relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300">
              <img
                src="/WhatsApp Image 2025-06-30 at 6.16.54 PM (1).jpeg"
                alt="Team Collaboration"
                className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity duration-300 flex items-end">
                <div className="p-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <h4 className="font-semibold">Team Collaboration</h4>
                  <p className="text-sm">Our research team working together on product development</p>
                </div>
              </div>
            </div>

            {/* Gallery Item 4 - Award Recognition */}
            <div className="group relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300">
              <img
                src="/WhatsApp Image 2025-06-30 at 6.16.53 PM.jpeg"
                alt="Award Recognition"
                className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity duration-300 flex items-end">
                <div className="p-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <h4 className="font-semibold">Award Recognition</h4>
                  <p className="text-sm">Francis receiving Top 40 Under 40 award for innovation</p>
                </div>
              </div>
            </div>

            {/* Gallery Item 5 - Community Outreach */}
            <div className="group relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300">
              <img
                src="/WhatsApp Image 2025-06-30 at 8.23.55 PM.jpeg"
                alt="Community Outreach"
                className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity duration-300 flex items-end">
                <div className="p-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <h4 className="font-semibold">Community Outreach</h4>
                  <p className="text-sm">Engaging with local community leaders and stakeholders</p>
                </div>
              </div>
            </div>

            {/* Gallery Item 6 - Product Showcase */}
            <div className="group relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300">
              <img
                src="/WhatsApp Image 2025-06-30 at 6.16.54 PM.jpeg"
                alt="Product showcase"
                className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity duration-300 flex items-end">
                <div className="p-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <h4 className="font-semibold">Our Products</h4>
                  <p className="text-sm">Fresh batch of nutritious fish cookies</p>
                </div>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center mt-12">
            <p className="text-gray-700 mb-6">Want to see more of our work or share your own Samaki Cookies experience?</p>
            <a href="#contact" className="bg-amber-700 text-white px-6 py-3 rounded-lg hover:bg-amber-800 transition">Get in Touch</a>
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <section id="story" className="py-16 bg-amber-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-amber-900 mb-4">Our Story</h2>
            <div className="w-24 h-1 bg-amber-500 mx-auto"></div>
          </div>
          <div className="flex flex-col md:flex-row items-center mb-16">
            <div className="md:w-1/2 md:pr-12 mb-8 md:mb-0">
              <h3 className="text-2xl font-semibold text-amber-800 mb-4">From Challenge to Innovation</h3>
              <p className="text-gray-700 mb-4">
                Francis Thoya's journey began when he lost his income during challenging times. Living in Kilifi County,
                a region with abundant fish resources but high malnutrition rates, he saw an opportunity to make a difference.
              </p>
              <p className="text-gray-700 mb-4">
                After experimenting with various recipes, Francis developed a unique method to incorporate fish into cookies
                without the fishy taste, creating a product that children love while providing essential nutrients they need.
              </p>
              <p className="text-gray-700">
                Today, Samaki Cookies is not just a business but a mission to combat malnutrition in Kenya while supporting
                local fishermen and creating employment opportunities in the community.
              </p>
            </div>
            <div className="md:w-1/2">
              <div className="bg-white p-8 rounded-lg shadow-lg">
                <h4 className="text-xl font-semibold text-amber-900 mb-4">Meet Our Founder</h4>
                <div className="flex items-start mb-6">
                  <img src="/WhatsApp Image 2025-07-01 at 1.18.53 AM.jpeg" alt="Francis Thoya" className="w-24 h-24 rounded-full object-cover mr-4" />
                  <div>
                    <h5 className="text-lg font-semibold text-amber-800">Francis Thoya</h5>
                    <p className="text-gray-700 italic">Founder & CEO</p>
                    <p className="text-gray-600 text-sm">M.A. in Sociology, Pwani University</p>
                    <p className="text-gray-700 mt-2">
                      "I believe that good nutrition shouldn't be a luxury. Our mission is to make nutritious food accessible
                      to all children, regardless of their background."
                    </p>
                  </div>
                </div>
                <div className="border-t border-amber-200 pt-6">
                  <h5 className="text-lg font-semibold text-amber-800 mb-3">Our Impact</h5>
                  <ul className="text-gray-700 space-y-2">
                    <li>✓ Providing nutrition to over 1,000 children</li>
                    <li>✓ Supporting 15+ local fishing families</li>
                    <li>✓ Creating employment for 8 community members</li>
                    <li>✓ Reducing food waste by utilizing local resources</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Co-founder Section */}
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2">
              <div className="bg-white p-8 rounded-lg shadow-lg">
                <h4 className="text-xl font-semibold text-amber-900 mb-4">Meet Our Co-founder</h4>
                <div className="flex items-start mb-6">
                  <img src="/1718318006298.jpeg" alt="Samuel Mweni" className="w-24 h-24 rounded-full object-cover mr-4" />
                  <div>
                    <h5 className="text-lg font-semibold text-amber-800">Samuel Mweni</h5>
                    <p className="text-gray-700 italic">Co-founder & Data Scientist</p>
                    <p className="text-gray-600 text-sm">American University Graduate</p>
                  </div>
                </div>
                <div className="border-t border-amber-200 pt-6">
                  <h5 className="text-lg font-semibold text-amber-800 mb-3">Personal Mission</h5>
                  <p className="text-gray-700 text-sm leading-relaxed">
                    "My passion for combating malnutrition stems from a deeply personal experience. When my younger sister was a child,
                    she suffered from severe malnutrition that affected her growth and development. Watching her struggle and seeing
                    the long-term impacts on her health motivated me to use my data science expertise to help solve this critical issue.
                    Through Samaki Cookies, we're not just creating a product - we're preventing other families from experiencing what
                    mine went through. Every cookie we make carries the hope that no child should suffer from preventable malnutrition."
                  </p>
                </div>
              </div>
            </div>
            <div className="md:w-1/2 md:pl-12 mt-8 md:mt-0">
              <h3 className="text-2xl font-semibold text-amber-800 mb-4">Data-Driven Nutrition</h3>
              <p className="text-gray-700 mb-4">
                Samuel brings his expertise in data science to optimize our nutritional formulations and track the impact
                of our products on child health outcomes. His analytical approach helps us understand malnutrition patterns
                and develop targeted solutions.
              </p>
              <p className="text-gray-700 mb-6">
                With his background from American University and personal experience with malnutrition's effects, Samuel
                ensures that every Samaki Cookie is scientifically formulated to provide maximum nutritional benefit while
                maintaining the taste children love.
              </p>
              <div className="bg-amber-100 p-4 rounded-lg">
                <h5 className="font-semibold text-amber-800 mb-2">Samuel's Contributions:</h5>
                <ul className="text-gray-700 space-y-1 text-sm">
                  <li>• Nutritional data analysis and optimization</li>
                  <li>• Health impact measurement and tracking</li>
                  <li>• Product formulation research</li>
                  <li>• Community health data collection</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16 bg-amber-100">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-amber-900 mb-4">What People Say</h2>
            <div className="w-24 h-1 bg-amber-500 mx-auto"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-amber-200 rounded-full flex items-center justify-center text-amber-800 font-bold text-xl">M</div>
                <div className="ml-4">
                  <h4 className="font-semibold text-amber-900">Mary Wanjiku</h4>
                  <p className="text-gray-600 text-sm">Parent</p>
                </div>
              </div>
              <p className="text-gray-700">
                "My children love these cookies and I love that they're getting important nutrients. It's been a game-changer for our family's nutrition."
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-amber-200 rounded-full flex items-center justify-center text-amber-800 font-bold text-xl">J</div>
                <div className="ml-4">
                  <h4 className="font-semibold text-amber-900">John Omondi</h4>
                  <p className="text-gray-600 text-sm">School Principal</p>
                </div>
              </div>
              <p className="text-gray-700">
                "Since introducing Samaki Cookies in our school feeding program, we've seen improved attendance and better concentration from our students."
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-amber-200 rounded-full flex items-center justify-center text-amber-800 font-bold text-xl">D</div>
                <div className="ml-4">
                  <h4 className="font-semibold text-amber-900">Dr. Amina Hassan</h4>
                  <p className="text-gray-600 text-sm">Nutritionist</p>
                </div>
              </div>
              <p className="text-gray-700">
                "Samaki Cookies represents an innovative approach to addressing malnutrition. The combination of protein and essential fatty acids is exactly what growing children need."
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-amber-900 mb-4">Contact Us</h2>
            <div className="w-24 h-1 bg-amber-500 mx-auto mb-6"></div>
            <p className="text-gray-700 max-w-2xl mx-auto">
              Interested in our products or have questions? We'd love to hear from you! Reach out to us through any of the following channels.
            </p>
          </div>
          <div className="flex flex-col md:flex-row">
            <div className="md:w-1/2 mb-8 md:mb-0 md:pr-8">
              <form onSubmit={handleSubmit} className="bg-amber-50 p-8 rounded-lg shadow-lg">
                <div className="mb-4">
                  <label htmlFor="name" className="block text-amber-900 font-medium mb-2">Your Name</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-2 border border-amber-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
                  />
                </div>
                <div className="mb-4">
                  <label htmlFor="email" className="block text-amber-900 font-medium mb-2">Email Address</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-2 border border-amber-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
                  />
                </div>
                <div className="mb-4">
                  <label htmlFor="message" className="block text-amber-900 font-medium mb-2">Message</label>
                  <textarea
                    id="message"
                    name="message"
                    rows={4}
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-2 border border-amber-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
                  ></textarea>
                </div>

                {submitStatus === 'success' && (
                  <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
                    Your email client should open with your message. If it doesn't, please email us <NAME_EMAIL>
                  </div>
                )}

                {submitStatus === 'error' && (
                  <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                    There was an error. Please try again or contact us <NAME_EMAIL>
                  </div>
                )}

                <div className="flex space-x-2">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="flex-1 bg-amber-700 text-white px-6 py-3 rounded-lg hover:bg-amber-800 transition disabled:opacity-50"
                  >
                    {isSubmitting ? 'Sending...' : 'Send Message'}
                  </button>
                  <button
                    type="button"
                    onClick={openWhatsAppContact}
                    className="bg-green-500 text-white px-4 py-3 rounded-lg hover:bg-green-600 transition flex items-center justify-center"
                    title="Contact via WhatsApp"
                  >
                    <FaWhatsapp size={20} />
                  </button>
                </div>
              </form>
            </div>
            <div className="md:w-1/2 md:pl-8">
              <div className="bg-amber-50 p-8 rounded-lg shadow-lg h-full">
                <h3 className="text-2xl font-semibold text-amber-900 mb-6">Get in Touch</h3>
                <div className="space-y-6">
                  <div className="flex items-start">
                    <FaPhone className="text-amber-700 mt-1 mr-4" />
                    <div>
                      <h4 className="font-semibold text-amber-800">Phone</h4>
                      <a
                        href="tel:+254704668776"
                        className="text-amber-700 hover:text-amber-900 transition"
                      >
                        +254-704 668 776
                      </a>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <FaWhatsapp className="text-green-600 mt-1 mr-4" />
                    <div>
                      <h4 className="font-semibold text-amber-800">WhatsApp</h4>
                      <button
                        onClick={openWhatsAppContact}
                        className="text-green-600 hover:text-green-800 transition"
                      >
                        +254-704 668 776
                      </button>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <FaEnvelope className="text-amber-700 mt-1 mr-4" />
                    <div>
                      <h4 className="font-semibold text-amber-800">Email</h4>
                      <a
                        href="mailto:<EMAIL>"
                        className="text-amber-700 hover:text-amber-900 transition"
                      >
                        <EMAIL>
                      </a>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <FaMapMarkerAlt className="text-amber-700 mt-1 mr-4" />
                    <div>
                      <h4 className="font-semibold text-amber-800">Location</h4>
                      <p className="text-gray-700">Kilifi County, Kenya</p>
                    </div>
                  </div>

                  {/* External Links Section */}
                  <div className="pt-4 border-t border-amber-200">
                    <h4 className="font-semibold text-amber-800 mb-3">Learn More About Us</h4>
                    <div className="space-y-3">
                      <a
                        href="https://www.facebook.com/share/p/15cJJJJJJJ/"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center text-amber-700 hover:text-amber-900 transition group"
                      >
                        <FaExternalLinkAlt className="mr-2 text-sm" />
                        <span className="group-hover:underline">Our Facebook Story</span>
                      </a>
                      <a
                        href="https://www.youtube.com/watch?v=hNzbQvwheoo"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center text-amber-700 hover:text-amber-900 transition group"
                      >
                        <FaExternalLinkAlt className="mr-2 text-sm" />
                        <span className="group-hover:underline">Watch Our Journey</span>
                      </a>
                    </div>
                  </div>

                  <div className="pt-4 border-t border-amber-200">
                    <h4 className="font-semibold text-amber-800 mb-3">Follow Us</h4>
                    <div className="flex space-x-4">
                      <a href="https://www.facebook.com/profile.php?id=100092713717056" target="_blank" rel="noopener noreferrer" className="text-amber-700 hover:text-amber-900 transition">
                        <FaFacebook size={24} />
                      </a>
                      <a href="https://twitter.com" className="text-amber-700 hover:text-amber-900 transition">
                        <FaTwitter size={24} />
                      </a>
                      <button
                        onClick={openWhatsAppContact}
                        className="text-green-600 hover:text-green-800 transition"
                        title="Contact us on WhatsApp"
                      >
                        <FaWhatsapp size={24} />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-amber-900 text-amber-100 py-12">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between">
            <div className="mb-8 md:mb-0">
              <h3 className="text-2xl font-bold mb-4">Samaki Cookies</h3>
              <p className="max-w-xs">Nutritious fish cookies made in Kenya, combating malnutrition one cookie at a time.</p>
            </div>
            <div className="mb-8 md:mb-0">
              <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2">
                <li><a href="#home" className="hover:text-white transition">Home</a></li>
                <li><a href="#about" className="hover:text-white transition">About</a></li>
                <li><a href="#products" className="hover:text-white transition">Products</a></li>
                <li><a href="#gallery" className="hover:text-white transition">Gallery</a></li>
                <li><a href="#story" className="hover:text-white transition">Our Story</a></li>
                <li><a href="#contact" className="hover:text-white transition">Contact</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Contact</h4>
              <ul className="space-y-2">
                <li className="flex items-center">
                  <FaPhone className="mr-2" />
                  <a href="tel:+254704668776" className="hover:text-white transition">
                    +254-704 668 776
                  </a>
                </li>
                <li className="flex items-center">
                  <FaWhatsapp className="mr-2" />
                  <button
                    onClick={openWhatsAppContact}
                    className="hover:text-white transition text-left"
                  >
                    WhatsApp Us
                  </button>
                </li>
                <li className="flex items-center">
                  <FaEnvelope className="mr-2" />
                  <a href="mailto:<EMAIL>" className="hover:text-white transition">
                    <EMAIL>
                  </a>
                </li>
                <li className="flex items-center">
                  <FaMapMarkerAlt className="mr-2" /> Kilifi County, Kenya
                </li>
              </ul>
              <div className="mt-4 flex space-x-4">
                <a href="https://www.facebook.com/profile.php?id=100092713717056" target="_blank" rel="noopener noreferrer" className="text-amber-200 hover:text-white transition">
                  <FaFacebook size={20} />
                </a>
                <a href="https://twitter.com" className="text-amber-200 hover:text-white transition">
                  <FaTwitter size={20} />
                </a>
                <button
                  onClick={openWhatsAppContact}
                  className="text-amber-200 hover:text-white transition"
                  title="Contact us on WhatsApp"
                >
                  <FaWhatsapp size={20} />
                </button>
              </div>
            </div>
          </div>
          <div className="border-t border-amber-800 mt-12 pt-8 text-center">
            <p>&copy; {new Date().getFullYear()} Samaki Cookies. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;